# ESP32-C3 串口RX功能说明

## 功能概述
本功能为ESP32-C3智能语音音箱项目添加了串口接收功能，可以接收外部设备通过串口发送的按键数据。

## 硬件配置
- **MCU**: ESP32-C3
- **UART端口**: UART1
- **RX引脚**: GPIO10
- **波特率**: 9600
- **数据格式**: 8N1 (8位数据位，无校验位，1个停止位)

## 数据格式
串口接收的数据格式与参考项目一致：
```
LC:XXXXXZ
```
其中：
- `LC:` - 固定帧头
- `XXXXX` - 5个字符的地址编码  
- `Z` - 1个字符的按键值(16进制)

例如：`LC:12345A` 表示地址12345，按键值A(十进制10)

## 功能特性
1. **自动初始化**: 系统启动时自动初始化串口RX功能
2. **按键消抖**: 内置按键消抖算法，避免重复触发
3. **数据解析**: 自动解析帧头和按键值
4. **格式转换**: 支持16进制按键值转10进制数字
5. **日志输出**: 详细的调试日志信息

## 全局变量
可以在其他模块中访问以下全局变量：
```c
extern char uart_rx_button_value;      // 按键值字符 ('0'-'9', 'A'-'F')
extern int uart_rx_button_value_int;   // 按键值整数 (0-15)
extern bool uart_rx_key_press;         // 按键按下标志
```

## 使用示例
```c
#include "uart_rx.h"

// 在其他模块中检查按键状态
if (uart_rx_key_press) {
    ESP_LOGI("Example", "按键按下: %c (十进制: %d)", 
             uart_rx_button_value, uart_rx_button_value_int);
    
    // 根据按键值执行相应操作
    switch (uart_rx_button_value_int) {
        case 0:
            // 处理按键0
            break;
        case 1:
            // 处理按键1  
            break;
        // ... 更多按键处理
    }
}
```

## 任务信息
- **任务名称**: "UART_RX_Task"
- **堆栈大小**: 4096字节
- **优先级**: 1
- **运行核心**: 自动分配
- **执行周期**: 100ms

## 测试方法
1. 连接串口调试工具到ESP32-C3的GPIO10引脚
2. 配置串口参数：9600,8,N,1
3. 发送测试数据：`LC:12345A`
4. 观察ESP32-C3的日志输出

## 注意事项
1. GPIO10原本被音频功放占用，现已将音频功放控制转移到AW9523B P0_5引脚
2. 串口数据必须包含正确的帧头"LC:"
3. 按键值必须是有效的16进制字符(0-9, A-F, a-f)
4. 系统仅支持RX功能，不支持TX发送
5. 音频功放现在由DeviceManager通过AW9523B自动控制

## 文件结构
```
main/boards/lichuang-c3-dev/
├── uart_rx.h              # 头文件
├── uart_rx.cc             # 实现文件
└── uart_rx_README.md      # 说明文档
```
