# 硬件配置修改说明

## 概述
为了支持433串口接收功能，对ESP32-C3立创开发板的硬件配置进行了调整。

## 主要修改

### 1. GPIO10用途变更
- **原用途**: 音频功放控制 (AUDIO_CODEC_PA_PIN)
- **新用途**: 433串口RX接收引脚 (UART_RX_PIN)

### 2. 音频功放控制转移
- **原方案**: ESP32-C3 GPIO10直接控制
- **新方案**: AW9523B IO扩展器 P0_5引脚控制
- **优势**: 释放ESP32-C3的GPIO资源，提高GPIO利用率

## 修改的文件

### 配置文件
- `config.h`: 
  - 将`AUDIO_CODEC_PA_PIN`从`GPIO_NUM_10`改为`-1`
  - 新增`AUDIO_CODEC_PA_AW9523_PIN`定义为`5`
  - 更新AW9523B配置注释

### DeviceManager类
- `device_manager.h`: 新增音频功放控制接口
  - `EnableAudioPA(bool enable)` - 控制音频功放开关
  - `IsAudioPAEnabled()` - 查询音频功放状态
- `device_manager.cc`: 实现音频功放控制逻辑
  - 通过AW9523B的P0_5引脚控制音频功放

### 音频编解码器
- `es8311_audio_codec.cc`: 修改`EnableOutput()`方法
  - 检测PA引脚是否为无效值(-1或GPIO_NUM_NC)
  - 如果无效，则通过DeviceManager控制AW9523B P0_5

### 433串口功能
- `uart_rx.h`: 定义GPIO10为433串口RX引脚
- `uart_rx.cc`: 实现433串口接收功能
- `uart_rx_README.md`: 使用说明文档

## 硬件连接图

```
ESP32-C3 引脚分配:
├── GPIO0/1   - I2C (音频codec)
├── GPIO2     - WS2812 LED  
├── GPIO3     - AW9523B RST
├── GPIO4-8   - I2S 音频接口
├── GPIO9     - 启动按钮
├── GPIO10    - 433串口RX ⭐新增
├── GPIO11    - AW9523B中断
└── GPIO12-21 - 可用

AW9523B 引脚分配:
├── P0_0~P0_3 - 按键输入 (SUCK/ON/ROCK/VOL)
├── P0_4      - 保留
├── P0_5      - 音频功放控制 ⭐新增
├── P0_6~P0_7 - 保留
└── P1_0~P1_7 - 电机/加热器控制
```

## 兼容性说明

### 音频功能
- ✅ 音频播放正常工作
- ✅ 音频功放自动控制
- ✅ 音量调节功能保持不变

### 433串口功能  
- ✅ 支持9600波特率接收
- ✅ 支持"LC:XXXXXZ"数据格式解析
- ✅ 按键消抖处理
- ✅ 16进制转10进制转换

### 现有功能
- ✅ AW9523B按键检测
- ✅ 电机控制功能
- ✅ 加热器控制功能
- ✅ LED指示功能

## 测试验证

### 音频功放测试
1. 播放音频时，AW9523B P0_5应输出高电平
2. 停止音频时，AW9523B P0_5应输出低电平
3. 音频质量应无影响

### 433串口测试
1. 连接串口工具到GPIO10
2. 配置9600,8,N,1参数
3. 发送`LC:12345A`测试数据
4. 验证日志输出和按键值解析

## 后续优化建议

1. **功耗优化**: 可考虑在空闲时关闭音频功放以节省功耗
2. **错误处理**: 增加AW9523B通信失败的恢复机制  
3. **状态同步**: 添加音频功放状态与系统状态的同步检查
4. **配置化**: 将GPIO和AW9523B引脚配置参数化，便于不同硬件版本适配

---
**修改日期**: 2024年12月
**修改人**: AI Assistant
**版本**: v1.0
