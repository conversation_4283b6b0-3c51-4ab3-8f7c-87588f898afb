#ifndef _APPLICATION_H_
#define _APPLICATION_H_

#include <freertos/FreeRTOS.h>
#include <freertos/event_groups.h>
#include <freertos/task.h>
#include <esp_timer.h>

#include <string>
#include <mutex>
#include <list>
#include <vector>
#include <deque>
#include <condition_variable>
#include <memory>

#include <opus_encoder.h>
#include <opus_decoder.h>
#include <opus_resampler.h>

#include "protocol.h"
#include "ota.h"
#include "background_task.h"
#include "audio_processor.h"
#include "wake_word.h"
#include "audio_debugger.h"

// 前向声明，降低耦合
class SerialTxService;

#define SCHEDULE_EVENT (1 << 0)
#define SEND_AUDIO_EVENT (1 << 1)
#define CHECK_NEW_VERSION_DONE_EVENT (1 << 2)

enum AecMode {
    kAecOff,
    kAecOnDeviceSide,
    kAecOnServerSide,
};

enum DeviceState {
    kDeviceStateUnknown,
    kDeviceStateStarting,
    kDeviceStateWifiConfiguring,
    kDeviceStateIdle,
    kDeviceStateConnecting,
    kDeviceStateListening,
    kDeviceStateSpeaking,
    kDeviceStateUpgrading,
    kDeviceStateActivating,
    kDeviceStateAudioTesting,
    kDeviceStateFatalError
};

#define OPUS_FRAME_DURATION_MS 60
#define MAX_AUDIO_PACKETS_IN_QUEUE 200  // 缓冲区解码音频
#define AUDIO_TESTING_MAX_DURATION_MS 10000
// 当解码队列达到上限时的“间隔抽帧”策略参数
// 每 AUDIO_THINNING_STRIDE 帧抽走 1 帧；一次最多抽走 AUDIO_THINNING_MAX_REMOVE 帧
#define AUDIO_THINNING_STRIDE 10
#define AUDIO_THINNING_MAX_REMOVE 20


class Application {
public:
    static Application& GetInstance() {
        static Application instance;
        return instance;
    }
    // 删除拷贝构造函数和赋值运算符
    Application(const Application&) = delete;
    Application& operator=(const Application&) = delete;

    void Start();
    DeviceState GetDeviceState() const { return device_state_; }
    bool IsVoiceDetected() const { return voice_detected_; }
    void Schedule(std::function<void()> callback);
    void SetDeviceState(DeviceState state);
    void Alert(const char* status, const char* message, const char* emotion = "", const std::string_view& sound = "");
    void DismissAlert();
    void AbortSpeaking(AbortReason reason);
    void ToggleChatState();
    void StartListening();
    void StopListening();
    void UpdateIotStates();
    void Reboot();
    void WakeWordInvoke(const std::string& wake_word);
    void PlaySound(const std::string_view& sound);
    bool CanEnterSleepMode();
    void SendMcpMessage(const std::string& payload);
    void SetAecMode(AecMode mode);
    AecMode GetAecMode() const { return aec_mode_; }
    BackgroundTask* GetBackgroundTask() const { return background_task_.get(); }

private:
    Application();
    ~Application();

    std::unique_ptr<WakeWord> wake_word_;
    std::unique_ptr<AudioProcessor> audio_processor_;
    std::unique_ptr<AudioDebugger> audio_debugger_;
    Ota ota_;
    std::mutex mutex_;
    std::list<std::function<void()>> main_tasks_;
    std::unique_ptr<Protocol> protocol_;
    EventGroupHandle_t event_group_ = nullptr;
    esp_timer_handle_t clock_timer_handle_ = nullptr;
    volatile DeviceState device_state_ = kDeviceStateUnknown;
    ListeningMode listening_mode_ = kListeningModeAutoStop;
    AecMode aec_mode_ = kAecOff;

    bool has_server_time_ = false;
    bool aborted_ = false;
    bool voice_detected_ = false;
    // 移除：bool busy_decoding_audio_ = false;  // 已用active_decode_tasks_替代
    int clock_ticks_ = 0;
    TaskHandle_t check_new_version_task_handle_ = nullptr;

    // 串口发送服务
    std::unique_ptr<SerialTxService> serial_tx_;
    esp_timer_handle_t serial_timer_handle_ = nullptr; // 10秒周期发送测试数据
    uint8_t serial_next_byte_ = 0x00; // 每次发送不同的8位数据（自增循环）

    // Audio encode / decode
    TaskHandle_t audio_loop_task_handle_ = nullptr;
    std::unique_ptr<BackgroundTask> background_task_;
    std::chrono::steady_clock::time_point last_output_time_;
    std::list<AudioStreamPacket> audio_send_queue_;
    // 优化：使用原始数据队列，避免AudioStreamPacket封装开销
    std::list<std::vector<uint8_t>> audio_decode_queue_;
    std::condition_variable audio_decode_cv_;
    std::list<AudioStreamPacket> audio_testing_queue_;

    // 新增：播放队列（PCM），用于解码/输出解耦
    static constexpr int MAX_PLAYBACK_TASKS_IN_QUEUE = 3;   // 队列上限=3
    static constexpr int PLAYBACK_HIGH_WATERMARK = 2;       // 到2停止解码
    static constexpr int PLAYBACK_LOW_WATERMARK  = 1;       // 回落到1恢复解码
    std::deque<std::vector<int16_t>> audio_playback_queue_;
    std::mutex playback_mutex_;
    std::condition_variable playback_cv_;
    std::atomic<bool> playback_backpressure_{false};         // 播放队列背压开关

    // 改进：并发解码控制，允许多个包同时处理
    std::atomic<int> active_decode_tasks_{0};  // 当前活跃的解码任务数
    static constexpr int MAX_CONCURRENT_DECODE_TASKS = 4;  // 最大并发解码任务数


    // 新增：用于维护音频包的timestamp队列
    std::list<uint32_t> timestamp_queue_;
    std::mutex timestamp_mutex_;

    std::unique_ptr<OpusEncoderWrapper> opus_encoder_;
    std::unique_ptr<OpusDecoderWrapper> opus_decoder_;

    OpusResampler input_resampler_;
    OpusResampler reference_resampler_;
    OpusResampler output_resampler_;

    void MainEventLoop();
    void OnAudioInput();
    void OnAudioOutput();
    bool ReadAudio(std::vector<int16_t>& data, int sample_rate, int samples);
    void ResetDecoder();
    void SetDecodeSampleRate(int sample_rate, int frame_duration);
    void CheckNewVersion();
    void ShowActivationCode(const std::string& code, const std::string& message);
    void OnClockTimer();
    void SetListeningMode(ListeningMode mode);
    void AudioLoop();
    void EnterAudioTestingMode();
    void ExitAudioTestingMode();



    // 串口测试：定时发送
    void OnSerialTimer();
};

#endif // _APPLICATION_H_
