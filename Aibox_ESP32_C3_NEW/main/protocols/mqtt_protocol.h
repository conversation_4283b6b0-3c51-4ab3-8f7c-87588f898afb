#ifndef MQTT_PROTOCOL_H
#define MQTT_PROTOCOL_H


#include "protocol.h"
#include <mqtt.h>
#include <udp.h>
#include <cJSON.h>
#include <mbedtls/aes.h>
#include <freertos/FreeRTOS.h>
#include <freertos/event_groups.h>

#include <functional>
#include <string>
#include <map>
#include <mutex>
#include <chrono>
#include "esp32_s3_szp.h"

#define MQTT_PING_INTERVAL_SECONDS 90
#define MQTT_RECONNECT_INTERVAL_MS 10000

#define MQTT_PROTOCOL_SERVER_HELLO_EVENT (1 << 0)
#define MWTT_PORT 1883


class MqttProtocol : public Protocol {
public:
    MqttProtocol();
    ~MqttProtocol();

    bool Start() override;
    bool SendAudio(const AudioStreamPacket& packet) override;
    bool OpenAudioChannel() override;
    void CloseAudioChannel() override;
    bool IsAudioChannelOpened() const override;

    //F移植 添加  ---使用C3原版 注释掉
    // void SetOnIncomingAudio(std::function<void(std::vector<uint8_t>&&)> callback) {
    //     on_incoming_audio_ = std::move(callback);
    // }
    //F移植 添加
    void SendCancelTTS(bool f=false );//发送取消tts消息
    //F移植 添加
    void SendImuStatesAndValue( const t_sQMI8658& imu_data,
        int touch_value);//发送陀螺仪数据

    //F移植 添加
    // 获取音量控制值并重置标志
    bool GetVolumeControl(std::string& value) {
        if (has_volume_control_) {
            value = volume_control_value_;
            has_volume_control_ = false;
            return true;
        }
        return false;
    }

    //F移植 添加     更新语言
    void UpdateLanguage(const std::string& language);
    void WakeupCall();

    // 音频传输统计结构体
    struct AudioTransmissionStats {
        uint32_t total_packets = 0;
        uint32_t total_chunks = 0;
        uint32_t failed_packets = 0;
        uint64_t total_bytes = 0;
        std::chrono::steady_clock::time_point last_transmission;
    };

    // 获取音频传输统计信息
    AudioTransmissionStats GetAudioStats() const {
        return audio_stats_;
    }

    // 重置音频传输统计
    void ResetAudioStats() {
        audio_stats_ = AudioTransmissionStats{};
    }

    // 打印音频传输统计信息（调试用）
    void LogAudioStats();


private:
    EventGroupHandle_t event_group_handle_;

    std::string endpoint_;
    std::string client_id_;
    std::string username_;
    std::string password_;
    std::string subscribe_topic_;
    std::string publish_topic_;
    std::string languagesType_; // 保存语言
    std::string user_id3_;

    // 新增：服务端VAD检测相关
    std::string vad_detection_topic_;

    std::mutex channel_mutex_;
    Mqtt* mqtt_ = nullptr;
    Udp* udp_ = nullptr;
    mbedtls_aes_context aes_ctx_;
    std::string aes_nonce_;
    std::string udp_server_;
    int udp_port_;
    uint32_t local_sequence_;
    uint32_t remote_sequence_;

    // 音量控制相关
    std::string volume_control_value_;
    bool has_volume_control_ = false;
    // 关机控制
    bool shutdown_requested_ = false;

    // 音频传输统计实例
    AudioTransmissionStats audio_stats_;

    bool StartMqttClient(bool report_error=false);
    void ParseServerHello(const cJSON* root);
    std::string DecodeHexString(const std::string& hex_string);

    bool SendText(const std::string& text) override;

    // 从 NVS 读取语言类型
    std::string LoadLanguageTypeFromNVS(const std::string& default_lang = "zh");

    // 保存语言类型到 NVS
    void SaveLanguageTypeToNVS(const std::string& language);

    // 处理服务端VAD检测消息
    void HandleVadDetectionMessage(const std::string& payload);
    void HandleServerVadDetection();

    std::string GetHelloMessage();
};


#endif // MQTT_PROTOCOL_H
